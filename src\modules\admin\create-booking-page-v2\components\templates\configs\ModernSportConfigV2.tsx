'use client'

import type { BookingField, FieldType } from '../../../types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { Clock, Image as ImageIcon, Phone, Plus, Settings, Trash2, Users } from 'lucide-react'
import React, { useCallback, useEffect, useState } from 'react'
import { useCreateBookingPageV2Store } from '../../../stores/create-booking-page-v2.store'

const FIELD_TYPES: { value: FieldType, label: string, icon: string }[] = [
  { value: 'football', label: 'Bóng đá', icon: '⚽' },
  { value: 'tennis', label: 'Tennis', icon: '🎾' },
  { value: 'badminton', label: 'Cầu lông', icon: '🏸' },
  { value: 'basketball', label: '<PERSON>óng rổ', icon: '🏀' },
]

// Memoized Field Item Component
const FieldItem = React.memo<{
  field: BookingField
  index: number
  onUpdate: (fieldId: string, updates: Partial<BookingField>) => void
  onRemove: (fieldId: string) => void
}>(({ field, index, onUpdate, onRemove }) => {
      const [localName, setLocalName] = useState(field.name)
      const debouncedName = useDebounce(localName, 300)

      useEffect(() => {
        if (debouncedName !== field.name) {
          onUpdate(field.id, { name: debouncedName })
        }
      }, [debouncedName, field.name, field.id, onUpdate])

      const handleTypeChange = useCallback((value: FieldType) => {
        onUpdate(field.id, { type: value })
      }, [field.id, onUpdate])

      const handleRemove = useCallback(() => {
        onRemove(field.id)
      }, [field.id, onRemove])

      return (
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="space-y-2">
              <Label>Tên sân</Label>
              <Input
                value={localName}
                onChange={e => setLocalName(e.target.value)}
                placeholder="VD: Sân VIP"
              />
            </div>

            <div className="space-y-2">
              <Label>Loại sân</Label>
              <Select
                value={field.type}
                onValueChange={handleTypeChange}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {FIELD_TYPES.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center gap-2">
                        <span>{type.icon}</span>
                        <span>{type.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Hành động</Label>
              <div className="flex items-center justify-between mb-3">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleRemove}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )
    })

// Custom hook for debouncing
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

const ModernSportConfig: React.FC = React.memo(() => {
  // Use direct store access
  const config = useCreateBookingPageV2Store(state => state.bookingConfig)
  const updateBookingConfig = useCreateBookingPageV2Store(state => state.updateBookingConfig)
  const addField = useCreateBookingPageV2Store(state => state.addField)
  const removeField = useCreateBookingPageV2Store(state => state.removeField)
  const updateField = useCreateBookingPageV2Store(state => state.updateField)

  // Local state for immediate UI updates
  const [localBannerTitle, setLocalBannerTitle] = useState(config.bannerTitle)
  const [localBannerSubtitle, setLocalBannerSubtitle] = useState(config.bannerSubtitle)

  // Debounced values
  const debouncedBannerTitle = useDebounce(localBannerTitle, 300)
  const debouncedBannerSubtitle = useDebounce(localBannerSubtitle, 300)

  // Update config when debounced values change
  useEffect(() => {
    if (debouncedBannerTitle !== config.bannerTitle) {
      updateBookingConfig({ bannerTitle: debouncedBannerTitle })
    }
  }, [debouncedBannerTitle, config.bannerTitle, updateBookingConfig])

  useEffect(() => {
    if (debouncedBannerSubtitle !== config.bannerSubtitle) {
      updateBookingConfig({ bannerSubtitle: debouncedBannerSubtitle })
    }
  }, [debouncedBannerSubtitle, config.bannerSubtitle, updateBookingConfig])

  // Memoized handlers
  const handleFieldAdd = useCallback(() => {
    addField()
  }, [addField])

  const handleFieldRemove = useCallback((fieldId: string) => {
    removeField(fieldId)
  }, [removeField])

  const handleFieldUpdate = useCallback((fieldId: string, updates: Partial<BookingField>) => {
    updateField(fieldId, updates)
  }, [updateField])

  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // In real app, upload to server and get URL
      const imageUrl = URL.createObjectURL(file)
      updateBookingConfig({ bannerImage: imageUrl })
    }
  }, [updateBookingConfig])

  const handleOpenTimeChange = useCallback((value: string) => {
    updateBookingConfig({ openTime: value })
  }, [updateBookingConfig])

  const handleCloseTimeChange = useCallback((value: string) => {
    updateBookingConfig({ closeTime: value })
  }, [updateBookingConfig])

  const handleRemoveBannerImage = useCallback(() => {
    updateBookingConfig({ bannerImage: '' })
  }, [updateBookingConfig])

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Settings className="w-6 h-6 text-orange-500" />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Cấu hình sân thể thao
          </h3>
          <p className="text-sm text-gray-600">
            Thiết lập thông tin cơ bản cho trang đặt sân
          </p>
        </div>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Thông tin cơ bản
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="banner-title">Tiêu đề banner</Label>
            <Input
              id="banner-title"
              value={localBannerTitle}
              onChange={e => setLocalBannerTitle(e.target.value)}
              placeholder="VD: CLB Cầu lông B-ZONE 11"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="banner-subtitle">Mô tả ngắn</Label>
            <Input
              id="banner-subtitle"
              value={localBannerSubtitle}
              onChange={e => setLocalBannerSubtitle(e.target.value)}
              placeholder="VD: Đặt sân cầu lông chất lượng cao"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="banner-image">Ảnh banner</Label>
            <div className="flex items-center gap-3">
              <Input
                id="banner-image"
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => document.getElementById('banner-image')?.click()}
                className="flex items-center gap-2"
              >
                <ImageIcon className="w-4 h-4" />
                Chọn ảnh
              </Button>
              {config.bannerImage && (
                <div className="flex items-center gap-2">
                  <img
                    src={config.bannerImage}
                    alt="Banner preview"
                    className="w-16 h-10 object-cover rounded border"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveBannerImage}
                  >
                    Xóa
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Operating Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Giờ hoạt động
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="open-time">Giờ mở cửa</Label>
              <Input
                id="open-time"
                type="time"
                value={config.openTime}
                onChange={e => handleOpenTimeChange(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="close-time">Giờ đóng cửa</Label>
              <Input
                id="close-time"
                type="time"
                value={config.closeTime}
                onChange={e => handleCloseTimeChange(e.target.value)}
              />
            </div>
          </div>
          <p className="text-sm text-gray-600">
            Lịch đặt sân sẽ hiển thị các khung giờ từ
            {' '}
            {config.openTime}
            {' '}
            đến
            {' '}
            {config.closeTime}
          </p>
        </CardContent>
      </Card>

      {/* Fields Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Users className="w-4 h-4" />
            Cấu hình sân (
            {config.fields.length}
            )
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.fields.length === 0
            ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>Chưa có sân nào được thêm</p>
                  <p className="text-sm">Nhấn "Thêm sân" để bắt đầu</p>
                </div>
              )
            : (
                <div className="space-y-3">
                  {config.fields.map((field, index) => (
                    <FieldItem
                      key={field.id}
                      field={field}
                      index={index}
                      onUpdate={handleFieldUpdate}
                      onRemove={handleFieldRemove}
                    />
                  ))}
                </div>
              )}

          <Separator />

          <Button
            type="button"
            variant="outline"
            onClick={handleFieldAdd}
            className="w-full border-dashed border-orange-300 text-orange-600 hover:bg-orange-50"
          >
            <Plus className="w-4 h-4 mr-2" />
            Thêm sân mới
          </Button>
        </CardContent>
      </Card>

      {/* Description Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Mô tả & Địa điểm
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="description">Mô tả chi tiết</Label>
            <Textarea
              id="description"
              value={config.description}
              onChange={e => updateBookingConfig({ description: e.target.value })}
              placeholder="VD: Sân thể thao hiện đại với đầy đủ tiện nghi..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="location">Địa điểm</Label>
            <Input
              id="location"
              value={config.location}
              onChange={e => updateBookingConfig({ location: e.target.value })}
              placeholder="VD: Số 123, Đường ABC, Quận 1, TP.HCM"
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Phone className="w-4 h-4" />
            Thông tin liên hệ
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contact-phone">Số điện thoại</Label>
              <Input
                id="contact-phone"
                value={config.contactInfo.phone}
                onChange={e => updateBookingConfig({
                  contactInfo: { ...config.contactInfo, phone: e.target.value },
                })}
                placeholder="VD: 0949 029 965"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="contact-email">Email</Label>
              <Input
                id="contact-email"
                type="email"
                value={config.contactInfo.email}
                onChange={e => updateBookingConfig({
                  contactInfo: { ...config.contactInfo, email: e.target.value },
                })}
                placeholder="VD: <EMAIL>"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="contact-address">Địa chỉ</Label>
            <Input
              id="contact-address"
              value={config.contactInfo.address}
              onChange={e => updateBookingConfig({
                contactInfo: { ...config.contactInfo, address: e.target.value },
              })}
              placeholder="VD: Số 40, đường số 11, phường Trường Thọ, TP.Thủ Đức"
            />
          </div>

          <Separator />

          <div className="space-y-4">
            <Label className="text-sm font-medium">Mạng xã hội (tùy chọn)</Label>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="facebook">Facebook</Label>
                <Input
                  id="facebook"
                  value={config.contactInfo.socialLinks.facebook || ''}
                  onChange={e => updateBookingConfig({
                    contactInfo: {
                      ...config.contactInfo,
                      socialLinks: { ...config.contactInfo.socialLinks, facebook: e.target.value },
                    },
                  })}
                  placeholder="https://facebook.com/..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="instagram">Instagram</Label>
                <Input
                  id="instagram"
                  value={config.contactInfo.socialLinks.instagram || ''}
                  onChange={e => updateBookingConfig({
                    contactInfo: {
                      ...config.contactInfo,
                      socialLinks: { ...config.contactInfo.socialLinks, instagram: e.target.value },
                    },
                  })}
                  placeholder="https://instagram.com/..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={config.contactInfo.socialLinks.website || ''}
                  onChange={e => updateBookingConfig({
                    contactInfo: {
                      ...config.contactInfo,
                      socialLinks: { ...config.contactInfo.socialLinks, website: e.target.value },
                    },
                  })}
                  placeholder="https://example.com"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
})

export default ModernSportConfig
